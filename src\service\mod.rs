
#[cfg(target_os = "linux")]
mod linux;
#[cfg(target_os = "windows")]
mod windows;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ServiceParams {
    pub ssh_port: u16,
}

pub async fn install_service(service_params: ServiceParams) -> anyhow::Result<()> {
    #[cfg(target_os = "linux")]
    {
        return linux::install_service(service_params).await
    }
    #[cfg(target_os = "windows")]
    {
        return windows::install_service(service_params).await
    }
    
    anyhow::bail!("Service mode is only supported on linux")
}
