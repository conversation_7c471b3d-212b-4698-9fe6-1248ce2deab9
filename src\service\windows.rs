use crate::ServiceParams;

#[cfg(target_os = "windows")]
pub async fn install_service(params: ServiceParams) -> anyhow::Result<()> {
    // Install the service first
    service::install(params)?;

    Ok(())
}

#[cfg(windows)]
mod service {
    use std::{ffi::OsString, path::Path, sync::mpsc, time::Duration};
    use std::os::windows::ffi::OsStrExt;

    use anyhow::Context as _;
    use windows::{
        core::PCWSTR,
        Win32::Security::Authorization::{
            SetNamedSecurityInfoW, SE_FILE_OBJECT,
        },
        Win32::Security::DACL_SECURITY_INFORMATION,
    };
    use windows_service::{
        define_windows_service,
        service::{
            ServiceAccess, ServiceControl, ServiceControlAccept, ServiceErrorControl, ServiceExitCode, ServiceInfo, ServiceStartType, ServiceState, ServiceStatus, ServiceType
        },
        service_control_handler::{self, ServiceControlHandlerResult},
        service_dispatcher, service_manager::{ServiceManager, ServiceManagerAccess},
    };

    use crate::ServiceParams;



    const SERVICE_NAME: &str = "iroh-ssh";
    const SERVICE_TYPE: ServiceType = ServiceType::OWN_PROCESS;
    const SERVICE_DISPLAY_NAME: &str = "iroh-ssh service";
    const SERVICE_DESCRIPTION: &str = "ssh without ip";
    const SERVICE_ACCOUNT_NAME: &str = "NT SERVICE\\iroh-ssh";

    pub fn install(params: ServiceParams) -> anyhow::Result<()> {

        let profile_ssh_dir = Path::new("C:\\Windows\\ServiceProfiles\\")
            .join(SERVICE_NAME)
            .join(".ssh");

        println!("Creating service data directory: {:?}", &profile_ssh_dir);
        std::fs::create_dir_all(&profile_ssh_dir)
            .with_context(|| format!("Failed to create service directory at {:?}", &profile_ssh_dir))?;

        println!("Setting permissions for '{}' on {:?}", SERVICE_ACCOUNT_NAME, &profile_ssh_dir);

        // Set file system permissions for the SSH directory
        set_ssh_directory_permissions(&profile_ssh_dir, SERVICE_ACCOUNT_NAME)
            .with_context(|| format!("Failed to set permissions on {:?}", &profile_ssh_dir))?;


        let manager_access = ServiceManagerAccess::CONNECT | ServiceManagerAccess::CREATE_SERVICE;
        let service_manager = ServiceManager::local_computer(None::<&str>, manager_access)?;
        let service_binary_path = ::std::env::current_exe()?
            .with_file_name("iroh-ssh.exe");
        
        let service_info = ServiceInfo {
            name: OsString::from(SERVICE_NAME),
            display_name: OsString::from(SERVICE_DISPLAY_NAME),
            service_type: ServiceType::OWN_PROCESS,
            start_type: ServiceStartType::AutoStart,
            error_control: ServiceErrorControl::Normal,
            executable_path: service_binary_path,
            launch_arguments: vec![OsString::from("server"), OsString::from("--ssh-port"), OsString::from(params.ssh_port.to_string()), OsString::from("--persist")],
            dependencies: vec![],
            account_name: Some(OsString::from(SERVICE_ACCOUNT_NAME)),
            account_password: None,
        };
        println!("owiejfoiwejf");

        let service = if let Ok(service) = service_manager.open_service(SERVICE_NAME, ServiceAccess::CHANGE_CONFIG | ServiceAccess::START) {
            service.change_config(&service_info)?;
            service
        } else {
            let res_service =
                service_manager.create_service(&service_info, ServiceAccess::CHANGE_CONFIG | ServiceAccess::START);
            if let Err(err) = res_service {
                println!("res_service: {:?}", err);
                return Err(anyhow::anyhow!("Failed to create service: {:?}", err));
            } else {
                res_service.unwrap()
            }
        };

        
        println!("owiejfoiwejf");

        service.set_description("ssh without ip (requires ssh server)")?;

        println!("✅ Service installed successfully!");
        println!("📋 Service Details:");
        println!("   Name: {}", SERVICE_NAME);
        println!("   Account: {}", SERVICE_ACCOUNT_NAME);
        println!("   Executable: {:?}", service_binary_path);

        // Try to start the service
        println!("🚀 Attempting to start the service...");
        let start_result = service.start(&[] as &[&str]);
        match start_result {
            Ok(()) => {
                println!("✅ Service started successfully!");
            }
            Err(err) => {
                println!("⚠️  Failed to start service: {:?}", err);

                // Check if it's a permission error
                if let windows_service::Error::Winapi(ref winapi_err) = err {
                    match winapi_err.raw_os_error() {
                        Some(5) => {
                            println!("\n❌ PERMISSION DENIED (Error Code 5)");
                            println!("📋 POSSIBLE SOLUTIONS:");
                            println!("   1. Ensure you're running as Administrator");
                            println!("   2. Try starting the service manually: sc start {}", SERVICE_NAME);
                            println!("   3. Check Windows Event Viewer for more details");
                            println!("   4. The service is installed but not started - you can start it later");
                        }
                        Some(1053) => {
                            println!("\n❌ SERVICE DID NOT RESPOND (Error Code 1053)");
                            println!("📋 This usually means the service executable has issues.");
                            println!("   The service is installed but may need debugging.");
                        }
                        Some(1056) => {
                            println!("\n❌ SERVICE ALREADY RUNNING (Error Code 1056)");
                            println!("✅ The service is already running - this is actually good!");
                        }
                        Some(code) => {
                            println!("\n❌ WINDOWS ERROR CODE: {}", code);
                            println!("📋 Try: sc start {} for more details", SERVICE_NAME);
                        }
                        None => {
                            println!("\n❌ UNKNOWN ERROR");
                            println!("📋 Try: sc start {} for more details", SERVICE_NAME);
                        }
                    }
                }

                println!("\n💡 The service has been installed successfully.");
                println!("   You can try to start it manually with: sc start {}", SERVICE_NAME);
                println!("   Or check the Windows Services management console.");

                // Don't return an error here - the service is installed, just not started
            }
        }

        Ok(())
    }

    pub fn run() -> windows_service::Result<()> {
        // Register generated `ffi_service_main` with the system and start the service, blocking
        // this thread until the service is stopped.
        service_dispatcher::start(SERVICE_NAME, ffi_service_main)
    }

    // Generate the windows service boilerplate.
    // The boilerplate contains the low-level service entry function (ffi_service_main) that parses
    // incoming service arguments into Vec<OsString> and passes them to user defined service
    // entry (my_service_main).
    define_windows_service!(ffi_service_main, my_service_main);

    // Service entry function which is called on background thread by the system with service
    // parameters. There is no stdout or stderr at this point so make sure to configure the log
    // output to file if needed.
    pub fn my_service_main(_arguments: Vec<OsString>) {
        if let Err(_e) = run_service() {
            // Handle the error, by logging or something.
        }
    }

    pub fn run_service() -> windows_service::Result<()> {
        // Create a channel to be able to poll a stop event from the service worker loop.
        let (shutdown_tx, shutdown_rx) = mpsc::channel();

        // Define system service event handler that will be receiving service events.
        let event_handler = move |control_event| -> ServiceControlHandlerResult {
            match control_event {
                // Notifies a service to report its current status information to the service
                // control manager. Always return NoError even if not implemented.
                ServiceControl::Interrogate => ServiceControlHandlerResult::NoError,

                // Handle stop
                ServiceControl::Stop => {
                    shutdown_tx.send(()).unwrap();
                    ServiceControlHandlerResult::NoError
                }

                // treat the UserEvent as a stop request
                ServiceControl::UserEvent(code) => {
                    if code.to_raw() == 130 {
                        shutdown_tx.send(()).unwrap();
                    }
                    ServiceControlHandlerResult::NoError
                }

                _ => ServiceControlHandlerResult::NotImplemented,
            }
        };

        // Register system service event handler.
        // The returned status handle should be used to report service status changes to the system.
        let status_handle = service_control_handler::register(SERVICE_NAME, event_handler)?;

        // Tell the system that service is running
        status_handle.set_service_status(ServiceStatus {
            service_type: SERVICE_TYPE,
            current_state: ServiceState::Running,
            controls_accepted: ServiceControlAccept::STOP,
            exit_code: ServiceExitCode::Win32(0),
            checkpoint: 0,
            wait_hint: Duration::default(),
            process_id: None,
        })?;

        
        tokio::spawn(async move {
            let _ = crate::api::server_mode(22, true).await;
        });

        // Poll shutdown event.
        let _ = shutdown_rx.recv();
        println!("Shutting down...");

        // Tell the system that service has stopped.
        status_handle.set_service_status(ServiceStatus {
            service_type: SERVICE_TYPE,
            current_state: ServiceState::Stopped,
            controls_accepted: ServiceControlAccept::empty(),
            exit_code: ServiceExitCode::Win32(0),
            checkpoint: 0,
            wait_hint: Duration::default(),
            process_id: None,
        })?;

        Ok(())
    }

    /// Set file system permissions for the SSH directory using Windows API directly
    fn set_ssh_directory_permissions(ssh_dir: &Path, service_account: &str) -> anyhow::Result<()> {
        println!("Setting file permissions for service account: {}", service_account);

        // Try the direct Windows API approach
        match set_permissions_via_win32_api(ssh_dir) {
            Ok(()) => {
                println!("✅ File permissions set successfully using Windows API");
                return Ok(());
            }
            Err(e) => {
                println!("⚠️  Windows API approach failed: {}", e);
                println!("\n📋 MANUAL PERMISSION SETUP REQUIRED:");
                println!("1. Run this installer as Administrator");
                println!("2. The service account '{}' needs full control over: {:?}", service_account, ssh_dir);
                println!("3. You can set this manually with:");
                println!("   icacls \"{}\" /grant \"{}:(OI)(CI)F\" /T", ssh_dir.display(), service_account);
                println!("\n💡 The directory has been created and the service will attempt to use it.");
                println!("   If permission issues occur, run the manual command above as Administrator.");
            }
        }

        Ok(())
    }

    /// Set permissions using Windows API directly
    fn set_permissions_via_win32_api(ssh_dir: &Path) -> anyhow::Result<()> {

        // Convert the path to a wide string for Windows API
        let path_wide: Vec<u16> = ssh_dir.as_os_str()
            .encode_wide()
            .chain(std::iter::once(0))
            .collect();

        // For Virtual Service Accounts, we can use SetNamedSecurityInfoW with NULL parameters
        // to grant the service account access. The Virtual Service Account will automatically
        // have the necessary permissions to access its own profile directory.

        unsafe {
            let result = SetNamedSecurityInfoW(
                PCWSTR(path_wide.as_ptr()),
                SE_FILE_OBJECT,
                DACL_SECURITY_INFORMATION,
                None,    // Owner - don't change
                None,    // Group - don't change
                None,    // DACL - use default (allows service account access)
                None,    // SACL - don't change
            );

            if result.is_err() {
                anyhow::bail!("SetNamedSecurityInfoW failed: {:?}", result);
            }
        }
        Ok(())
    }
}
