[package]
name = "iroh-ssh"
description = "ssh without ip"
license = "MIT"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
repository = "https://github.com/rustonbsd/iroh-ssh"
readme = "README.md"
keywords = ["networking"]
categories = ["network-programming"]
version = "0.2.1"
edition = "2024"

[dependencies]
anyhow = "1"
iroh = "0.35"
ed25519-dalek = { version = "2.0.0", features = ["rand_core"] }
rand = "0.8"
tokio-stream = { version = "0.1.15", features = ["sync"] }
tracing = "0.1"
tracing-subscriber = "0.3"
tokio = { version = "1", features = [
    "macros",
    "io-util",
    "sync",
    "rt",
] }
clap = { version = "4", features = ["derive"] }
homedir = "0.3.6"
z32 = "1.3"

[target.'cfg(target_os = "windows")'.dependencies]
windows-service = { version = "0.8.0"}
windows = { version = "0.58", features = [
    "Win32_Foundation",
    "Win32_Security",
    "Win32_Security_Authorization",
    "Win32_System_Memory",
    "Win32_System_SystemServices",
    "Win32_Security_Authentication_Identity",
    "Win32_Security_LsaPolicy"
] }

[profile.release]
opt-level = 3
lto = true
panic = "abort"